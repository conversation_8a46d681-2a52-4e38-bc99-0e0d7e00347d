import { useState, useEffect } from "react";
import { FaSearch } from "react-icons/fa";

interface FilterBarProps {
  onFilterChange: (filters: {
    platform: string;
    replyStatus: string;
    sortBy: string;
    search: string;
  }) => void;
}

export default function FilterBar({ onFilterChange }: FilterBarProps) {
  const [platform, setPlatform] = useState("all");
  const [replyStatus, setReplyStatus] = useState("all");
  const [sortBy, setSortBy] = useState("date-desc");
  const [search, setSearch] = useState("");

  // Apply filters whenever any of the filter values change
  useEffect(() => {
    onFilterChange({
      platform,
      replyStatus,
      sortBy,
      search
    });
  // We're purposely omitting onFilterChange from the dependency array to avoid an infinite loop
  // since this function may change on each render in the parent component
  }, [platform, replyStatus, sortBy, search]);

  return (
    <div className="bg-white shadow rounded-lg p-4 mt-6">
      <div className="grid gap-4 md:grid-cols-4 grid-cols-1">
        <div>
          <label htmlFor="platform-filter" className="block text-sm font-medium text-neutral-700">Platform</label>
          <select 
            id="platform-filter" 
            value={platform}
            onChange={(e) => setPlatform(e.target.value)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-neutral-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
          >
            <option value="all">All Platforms</option>
            <option value="google">Google</option>
            <option value="booking">Booking.com</option>
            <option value="airbnb">Airbnb</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="reply-status" className="block text-sm font-medium text-neutral-700">Reply Status</label>
          <select 
            id="reply-status" 
            value={replyStatus}
            onChange={(e) => setReplyStatus(e.target.value)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-neutral-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
          >
            <option value="all">All Reviews</option>
            <option value="replied">Replied</option>
            <option value="not-replied">Not Replied</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="sort-by" className="block text-sm font-medium text-neutral-700">Sort By</label>
          <select 
            id="sort-by" 
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-neutral-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
          >
            <option value="date-desc">Date (Newest First)</option>
            <option value="date-asc">Date (Oldest First)</option>
            <option value="rating-desc">Rating (Highest First)</option>
            <option value="rating-asc">Rating (Lowest First)</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-neutral-700">Search Reviews</label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="text-neutral-400" />
            </div>
            <input 
              type="text" 
              id="search" 
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="focus:ring-primary focus:border-primary block w-full pl-10 sm:text-sm border-neutral-300 rounded-md" 
              placeholder="Keywords..."
            />
          </div>
        </div>
      </div>
    </div>
  );
}
