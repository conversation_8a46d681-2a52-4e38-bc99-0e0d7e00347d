import { useState } from "react";
import { FaGoogle, FaBuilding, FaAirbnb, FaStar, FaRegStar, FaStarHalfAlt } from "react-icons/fa";
import ReviewReplyForm from "./ReviewReplyForm";
import { format } from "date-fns";

interface ReviewProps {
  id: number;
  platform: string;
  authorName: string;
  authorImage?: string;
  rating: number;
  date: string;
  content: string;
  isReplied: boolean;
  reply?: {
    content: string;
    date: string;
    userId: number;
  };
}

export default function ReviewCard({ 
  id, 
  platform, 
  authorName, 
  authorImage, 
  rating, 
  date, 
  content, 
  isReplied, 
  reply 
}: ReviewProps) {
  
  // Function to render stars based on rating
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    // Add full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push(<FaStar key={`full-${i}`} className="text-yellow-400 text-sm" />);
    }

    // Add half star if needed
    if (hasHalfStar) {
      stars.push(<FaStarHalfAlt key="half" className="text-yellow-400 text-sm" />);
    }

    // Add empty stars to reach 5
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<FaRegStar key={`empty-${i}`} className="text-yellow-400 text-sm" />);
    }

    return stars;
  };

  // Platform-specific styling and icons
  const getPlatformDetails = (platform: string) => {
    switch (platform) {
      case "google":
        return {
          icon: <FaGoogle className="mr-1" />,
          color: "bg-blue-100 text-blue-800",
          borderColor: "platform-google border-l-[#4285F4]"
        };
      case "booking":
        return {
          icon: <FaBuilding className="mr-1" />,
          color: "bg-blue-100 text-blue-800",
          borderColor: "platform-booking border-l-[#003580]"
        };
      case "airbnb":
        return {
          icon: <FaAirbnb className="mr-1" />,
          color: "bg-red-100 text-red-800",
          borderColor: "platform-airbnb border-l-[#FF5A5F]"
        };
      default:
        return {
          icon: <FaGoogle className="mr-1" />,
          color: "bg-gray-100 text-gray-800",
          borderColor: "border-l-gray-500"
        };
    }
  };

  const platformDetails = getPlatformDetails(platform);
  const formattedDate = format(new Date(date), 'MMMM d, yyyy');
  const replyDate = reply ? format(new Date(reply.date), 'MMMM d, yyyy') : '';

  return (
    <div className={`bg-white shadow rounded-lg overflow-hidden border-l-4 ${platformDetails.borderColor}`}>
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start">
            <div className="h-10 w-10 rounded-full bg-neutral-200 flex items-center justify-center overflow-hidden">
              {authorImage ? (
                <img src={authorImage} alt={authorName} className="h-full w-full object-cover" />
              ) : (
                <span className="text-neutral-600 font-semibold">{authorName.charAt(0)}</span>
              )}
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-neutral-900">{authorName}</h3>
              <div className="flex items-center mt-1">
                <div className="flex">
                  {renderStars(rating)}
                </div>
                <span className="ml-2 text-xs text-neutral-500">{formattedDate}</span>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${platformDetails.color}`}>
              {platformDetails.icon}
              {platform.charAt(0).toUpperCase() + platform.slice(1)}
            </span>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${isReplied ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              {isReplied ? 'Replied' : 'Not Replied'}
            </span>
          </div>
        </div>
        
        <div className="mt-3 text-sm text-neutral-700">
          {content}
        </div>
        
        {/* Reply section - already replied */}
        {isReplied && reply && (
          <div className="mt-4 bg-neutral-50 p-3 rounded-md border border-neutral-200">
            <div className="flex items-start">
              <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center text-primary">
                S
              </div>
              <div className="ml-3 flex-1">
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium text-neutral-900">Hotel Majestic Staff</h4>
                  <span className="text-xs text-neutral-500">{replyDate}</span>
                </div>
                <div className="mt-1 text-sm text-neutral-700">
                  {reply.content}
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Reply form - not replied yet */}
        {!isReplied && <ReviewReplyForm reviewId={id} />}
      </div>
    </div>
  );
}
