import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaGoogle, FaBuilding, FaAirbnb, FaTripadvisor, FaPlug } from "react-icons/fa";

interface PlatformConnectionModalProps {
  isOpen: boolean;
  onClose: (success?: boolean) => void;
  platformId?: string | null;
}

export default function PlatformConnectionModal({ isOpen, onClose, platformId }: PlatformConnectionModalProps) {
  // In a real application, these would connect to OAuth flows
  const handleConnectPlatform = (platform: string) => {
    // This would be replaced with actual OAuth flow
    console.log(`Connecting to ${platform}`);
    // For now, just close the modal with success
    onClose(true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-primary-100">
            <FaPlug className="text-primary text-lg" />
          </div>
          <DialogTitle className="text-center pt-4">Connect New Platform</DialogTitle>
          <DialogDescription className="text-center">
            Select a platform to connect and follow the authentication steps to import reviews from that platform.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4 space-y-3">
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleConnectPlatform("Google Business")}
          >
            <FaGoogle className="text-[#4285F4] mr-2 text-lg" />
            Connect with Google Business
          </Button>
          
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleConnectPlatform("Booking.com")}
          >
            <FaBuilding className="text-[#003580] mr-2 text-lg" />
            Connect with Booking.com
          </Button>
          
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleConnectPlatform("Airbnb")}
          >
            <FaAirbnb className="text-[#FF5A5F] mr-2 text-lg" />
            Connect with Airbnb
          </Button>
          
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleConnectPlatform("TripAdvisor")}
          >
            <FaTripadvisor className="text-[#00AA6C] mr-2 text-lg" />
            Connect with TripAdvisor
          </Button>
        </div>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={() => onClose()}
          >
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
