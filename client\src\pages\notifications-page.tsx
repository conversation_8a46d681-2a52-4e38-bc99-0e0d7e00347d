import Layout from "@/components/Layout";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";
import { Bell, Check, Mail } from "lucide-react";

// Sample notification data
const SAMPLE_NOTIFICATIONS = [
  {
    id: 1,
    type: "review",
    message: "New review from <PERSON> on Google Business",
    time: "2 hours ago",
    read: false,
    platform: "google"
  },
  {
    id: 2,
    type: "reply",
    message: "Your reply has been posted to <PERSON>'s review on Booking.com",
    time: "1 day ago",
    read: true,
    platform: "booking"
  },
  {
    id: 3,
    type: "alert",
    message: "New negative review (2 stars) requires attention on Airbnb",
    time: "3 days ago",
    read: false,
    platform: "airbnb"
  },
  {
    id: 4,
    type: "system",
    message: "Platform connection with Booking.com was successful",
    time: "1 week ago",
    read: true,
    platform: "system"
  }
];

export default function NotificationsPage() {
  const { user, isLoading } = useAuth();
  const [notifications, setNotifications] = useState(SAMPLE_NOTIFICATIONS);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const markAsRead = (id: number) => {
    setNotifications(notifications.map(n => 
      n.id === id ? { ...n, read: true } : n
    ));
  };

  const markAllAsRead = () => {
    setNotifications(notifications.map(n => ({ ...n, read: true })));
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-neutral-800">Notifications</h1>
            <p className="text-neutral-500">Stay updated with your review activities</p>
          </div>
          {unreadCount > 0 && (
            <button 
              onClick={markAllAsRead}
              className="text-primary hover:text-primary-600 font-medium text-sm flex items-center"
            >
              <Check className="h-4 w-4 mr-1" />
              Mark all as read
            </button>
          )}
        </div>

        {notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center bg-neutral-50 rounded-lg p-8 mt-8">
            <Mail className="h-12 w-12 text-neutral-400 mb-4" />
            <h3 className="text-xl font-medium text-neutral-700">No notifications</h3>
            <p className="text-neutral-500 text-center mt-2">
              You're all caught up! New notifications will appear here.
            </p>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <ul className="divide-y divide-neutral-200">
              {notifications.map((notification) => (
                <li 
                  key={notification.id}
                  className={`p-4 hover:bg-neutral-50 transition-colors ${!notification.read ? 'bg-blue-50' : ''}`}
                >
                  <div className="flex items-start">
                    <div className={`flex-shrink-0 p-2 rounded-full ${getIconBackground(notification.platform)}`}>
                      <Bell className="h-5 w-5 text-white" />
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="flex items-center justify-between">
                        <p className={`text-sm ${!notification.read ? 'font-semibold text-neutral-900' : 'text-neutral-700'}`}>
                          {notification.message}
                        </p>
                        <span className="text-xs text-neutral-500">{notification.time}</span>
                      </div>
                      <div className="mt-1 flex items-center">
                        {!notification.read && (
                          <Badge className="bg-blue-100 hover:bg-blue-200 text-blue-800 border-none mr-2 px-2 py-0 text-xs">
                            New
                          </Badge>
                        )}
                        <span className="text-xs text-neutral-500">
                          {notification.type === 'review' ? 'New Review' : 
                           notification.type === 'reply' ? 'Reply Posted' : 
                           notification.type === 'alert' ? 'Alert' : 'System'}
                        </span>
                      </div>
                    </div>
                    {!notification.read && (
                      <button 
                        onClick={() => markAsRead(notification.id)}
                        className="ml-4 text-xs text-primary hover:text-primary-600"
                      >
                        Mark as read
                      </button>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </Layout>
  );
}

function getIconBackground(platform: string) {
  switch (platform) {
    case 'google':
      return 'bg-[#4285F4]';
    case 'booking':
      return 'bg-[#003580]';
    case 'airbnb':
      return 'bg-[#FF5A5F]';
    default:
      return 'bg-gray-500';
  }
}