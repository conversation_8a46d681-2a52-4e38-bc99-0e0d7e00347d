import { pgTable, text, serial, integer, boolean, timestamp, pgEnum } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User schema for authentication
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  fullName: text("full_name").notNull(),
  role: text("role").default("staff"),
  hotelId: integer("hotel_id"),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  fullName: true,
});

// Platform enum for review sources
export const platformEnum = pgEnum("platform", ["google", "booking", "airbnb"]);

// Hotels schema for managing multiple hotels
export const hotels = pgTable("hotels", {
  id: serial("id").primary<PERSON>ey(),
  name: text("name").notNull(),
  address: text("address").notNull(),
});

export const insertHotelSchema = createInsertSchema(hotels).pick({
  name: true,
  address: true,
});

// OAuth tokens for platform connections
export const platformTokens = pgTable("platform_tokens", {
  id: serial("id").primaryKey(),
  hotelId: integer("hotel_id").notNull(),
  platform: text("platform").notNull(),
  accessToken: text("access_token").notNull(),
  refreshToken: text("refresh_token"),
  expiresAt: timestamp("expires_at"),
});

export const insertPlatformTokenSchema = createInsertSchema(platformTokens).pick({
  hotelId: true,
  platform: true,
  accessToken: true,
  refreshToken: true,
  expiresAt: true,
});

// Reviews schema
export const reviews = pgTable("reviews", {
  id: serial("id").primaryKey(),
  hotelId: integer("hotel_id").notNull(),
  platform: text("platform").notNull(),
  externalId: text("external_id").notNull(),
  authorName: text("author_name").notNull(),
  authorImage: text("author_image"),
  rating: integer("rating").notNull(),
  content: text("content").notNull(),
  date: timestamp("date").notNull(),
  isReplied: boolean("is_replied").default(false),
});

export const insertReviewSchema = createInsertSchema(reviews).pick({
  hotelId: true,
  platform: true,
  externalId: true, 
  authorName: true,
  authorImage: true,
  rating: true,
  content: true,
  date: true,
});

// Replies schema
export const replies = pgTable("replies", {
  id: serial("id").primaryKey(),
  reviewId: integer("review_id").notNull(),
  content: text("content").notNull(),
  userId: integer("user_id").notNull(),
  date: timestamp("date").notNull().defaultNow(),
  isPosted: boolean("is_posted").default(false),
});

export const insertReplySchema = createInsertSchema(replies).pick({
  reviewId: true,
  content: true,
  userId: true,
});

// Type definitions
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export type InsertHotel = z.infer<typeof insertHotelSchema>;
export type Hotel = typeof hotels.$inferSelect;

export type InsertPlatformToken = z.infer<typeof insertPlatformTokenSchema>;
export type PlatformToken = typeof platformTokens.$inferSelect;

export type InsertReview = z.infer<typeof insertReviewSchema>;
export type Review = typeof reviews.$inferSelect & { 
  reply?: Reply 
};

export type InsertReply = z.infer<typeof insertReplySchema>;
export type Reply = typeof replies.$inferSelect;

// Filters and sorting types for the API
export const reviewFilterSchema = z.object({
  platform: z.enum(['all', 'google', 'booking', 'airbnb']).optional(),
  replyStatus: z.enum(['all', 'replied', 'not-replied']).optional(),
  sortBy: z.enum(['date-desc', 'date-asc', 'rating-desc', 'rating-asc']).optional(),
  search: z.string().optional(),
  page: z.number().optional(),
  limit: z.number().optional(),
});

export type ReviewFilter = z.infer<typeof reviewFilterSchema>;
