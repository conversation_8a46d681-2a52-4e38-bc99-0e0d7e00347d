import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { FaDownload } from 'react-icons/fa';
import { useToast } from '@/hooks/use-toast';
import { downloadCSV, downloadPDF } from '@/lib/exportUtils';
import { Review } from '@shared/schema';

interface ExportOptionsProps {
  reviews: Review[];
  isLoading?: boolean;
}

export default function ExportOptions({ reviews, isLoading = false }: ExportOptionsProps) {
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);

  const handleExportCSV = async () => {
    try {
      setIsExporting(true);
      downloadCSV(reviews);
      toast({
        title: 'Export Successful',
        description: 'Reviews have been exported to CSV',
      });
    } catch (error) {
      console.error('Failed to export CSV:', error);
      toast({
        title: 'Export Failed',
        description: 'There was an error exporting to CSV',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportPDF = async () => {
    try {
      setIsExporting(true);
      await downloadPDF(reviews);
      toast({
        title: 'Export Successful',
        description: 'Reviews have been exported to PDF',
      });
    } catch (error) {
      console.error('Failed to export PDF:', error);
      toast({
        title: 'Export Failed',
        description: 'There was an error exporting to PDF',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" disabled={isLoading || isExporting || reviews.length === 0}>
          <FaDownload className="-ml-1 mr-2 h-5 w-5 text-neutral-500" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleExportCSV} disabled={isExporting || reviews.length === 0}>
          Export as CSV
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleExportPDF} disabled={isExporting || reviews.length === 0}>
          Export as PDF
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}