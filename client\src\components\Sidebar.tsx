import { useAuth } from "@/hooks/use-auth";
import { FaHotel, FaStarHalfAlt, FaBell, FaCog, FaChartLine, FaSignOutAlt, FaPlusCircle } from "react-icons/fa";
import { FaBuilding } from "react-icons/fa";
import { FaAirbnb, FaGoogle } from "react-icons/fa";
import { useLocation } from "wouter";
import { useState } from "react";
import PlatformConnectionModal from "./PlatformConnectionModal";

export default function Sidebar() {
  const { user, logoutMutation } = useAuth();
  const [location] = useLocation();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  return (
    <>
      <aside className="hidden md:flex md:flex-col bg-white shadow-lg z-10 w-64 flex-shrink-0">
        <div className="flex items-center justify-center h-16 border-b border-neutral-200">
          <h1 className="text-xl font-semibold text-primary">
            <FaHotel className="inline mr-2" />Hotel Review Manager
          </h1>
        </div>
        
        <div className="overflow-y-auto custom-scrollbar flex-grow">
          <nav className="mt-5 px-2">
            <a 
              href="/"
              className={`group flex items-center px-2 py-2 text-base font-medium rounded-md ${
                location === "/" 
                  ? "bg-primary-50 text-primary" 
                  : "text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900"
              }`}
            >
              <FaStarHalfAlt className={`mr-3 ${location === "/" ? "text-primary" : "text-neutral-400 group-hover:text-neutral-500"}`} />
              Dashboard
            </a>
            <a 
              href="/notifications" 
              className={`group flex items-center px-2 py-2 text-base font-medium rounded-md ${
                location === "/notifications" 
                  ? "bg-primary-50 text-primary" 
                  : "text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900"
              }`}
            >
              <FaBell className={`mr-3 ${location === "/notifications" ? "text-primary" : "text-neutral-400 group-hover:text-neutral-500"}`} />
              Notifications
            </a>
            <a 
              href="/settings" 
              className={`group flex items-center px-2 py-2 text-base font-medium rounded-md ${
                location === "/settings" 
                  ? "bg-primary-50 text-primary" 
                  : "text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900"
              }`}
            >
              <FaCog className={`mr-3 ${location === "/settings" ? "text-primary" : "text-neutral-400 group-hover:text-neutral-500"}`} />
              Settings
            </a>
            <a 
              href="/analytics" 
              className={`group flex items-center px-2 py-2 text-base font-medium rounded-md ${
                location === "/analytics" 
                  ? "bg-primary-50 text-primary" 
                  : "text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900"
              }`}
            >
              <FaChartLine className={`mr-3 ${location === "/analytics" ? "text-primary" : "text-neutral-400 group-hover:text-neutral-500"}`} />
              Analytics
            </a>

            <div className="mt-8">
              <h3 className="px-3 text-xs font-semibold text-neutral-500 uppercase tracking-wider">
                Connected Platforms
              </h3>
              <div className="mt-1 space-y-1">
                <a 
                  href="/platforms" 
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    location === "/platforms" 
                      ? "bg-primary-50 text-primary" 
                      : "text-neutral-700 hover:bg-neutral-50"
                  }`}
                >
                  <FaGoogle className={`mr-3 ${location === "/platforms" ? "text-primary" : "text-[#4285F4]"}`} />
                  Google Business
                </a>
                <a 
                  href="/platforms" 
                  className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-neutral-700 hover:bg-neutral-50"
                >
                  <FaBuilding className="text-[#003580] mr-3" />
                  Booking.com
                </a>
                <a 
                  href="/platforms" 
                  className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-neutral-700 hover:bg-neutral-50"
                >
                  <FaAirbnb className="text-[#FF5A5F] mr-3" />
                  Airbnb
                </a>
                <button 
                  onClick={() => setIsModalOpen(true)}
                  className="w-full group flex items-center px-2 py-2 text-sm font-medium rounded-md text-primary hover:bg-neutral-50"
                >
                  <FaPlusCircle className="mr-3" />
                  Connect New Platform
                </button>
              </div>
            </div>
          </nav>
        </div>
        
        {/* User profile section */}
        <div className="flex items-center p-4 border-t border-neutral-200">
          <a href="/profile" className="flex items-center flex-grow">
            <div className="h-8 w-8 rounded-full bg-primary-200 flex items-center justify-center text-primary">
              {user?.fullName?.[0] || user?.username?.[0] || "U"}
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-neutral-700">{user?.fullName || user?.username}</p>
              <p className="text-xs font-medium text-neutral-500">Hotel Manager</p>
            </div>
          </a>
          <button 
            onClick={handleLogout}
            className="text-neutral-400 hover:text-neutral-500"
          >
            <FaSignOutAlt />
          </button>
        </div>
      </aside>

      {/* Mobile navbar - only visible on small screens */}
      <div className="md:hidden bg-white border-b border-neutral-200 fixed top-0 inset-x-0 z-10">
        <div className="flex items-center justify-between h-16 px-4">
          <h1 className="text-lg font-semibold text-primary">
            <FaHotel className="inline mr-2" />Hotel Review Manager
          </h1>
          <button className="text-neutral-500 focus:outline-none">
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      {/* Platform Connection Modal */}
      <PlatformConnectionModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </>
  );
}
