import Layout from "@/components/Layout";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { CalendarIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { addDays, format } from "date-fns";
import { useState } from "react";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer
} from "recharts";

// Sample data for charts
const monthlyRatingsData = [
  { name: "<PERSON>", rating: 4.2 },
  { name: "Feb", rating: 4.3 },
  { name: "<PERSON>", rating: 4.1 },
  { name: "Apr", rating: 4.4 },
  { name: "May", rating: 4.5 },
  { name: "Jun", rating: 4.6 },
  { name: "Jul", rating: 4.4 },
  { name: "Aug", rating: 4.3 },
  { name: "Sep", rating: 4.7 },
  { name: "Oct", rating: 4.8 },
  { name: "Nov", rating: 4.6 },
  { name: "Dec", rating: 4.5 },
];

const platformDistributionData = [
  { name: "Google", value: 42 },
  { name: "Booking.com", value: 35 },
  { name: "Airbnb", value: 23 },
];

const PLATFORM_COLORS = ["#4285F4", "#003580", "#FF5A5F"];

const sentimentData = [
  { name: "Positive", count: 68 },
  { name: "Neutral", count: 24 },
  { name: "Negative", count: 8 },
];

const SENTIMENT_COLORS = ["#4ade80", "#94a3b8", "#f87171"];

const reviewVolumeData = [
  { name: "Jan", google: 14, booking: 10, airbnb: 5 },
  { name: "Feb", google: 16, booking: 12, airbnb: 7 },
  { name: "Mar", google: 18, booking: 14, airbnb: 9 },
  { name: "Apr", google: 15, booking: 11, airbnb: 8 },
  { name: "May", google: 17, booking: 13, airbnb: 10 },
  { name: "Jun", google: 20, booking: 17, airbnb: 12 },
];

const topPositiveKeywords = [
  { keyword: "Comfortable", count: 42 },
  { keyword: "Staff", count: 38 },
  { keyword: "Clean", count: 35 },
  { keyword: "Location", count: 30 },
  { keyword: "Breakfast", count: 25 },
];

const topNegativeKeywords = [
  { keyword: "Noise", count: 15 },
  { keyword: "Bathroom", count: 12 },
  { keyword: "Price", count: 10 },
  { keyword: "WiFi", count: 8 },
  { keyword: "Parking", count: 6 },
];

export default function AnalyticsPage() {
  const { user, isLoading } = useAuth();
  const [dateRange, setDateRange] = useState<{
    from: Date;
    to: Date;
  }>({
    from: addDays(new Date(), -30),
    to: new Date(),
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-neutral-800">Analytics</h1>
            <p className="text-neutral-500">Track your review performance over time</p>
          </div>

          <div className="flex items-center">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="justify-start text-left font-normal w-[260px]"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateRange?.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "LLL dd, y")} -{" "}
                        {format(dateRange.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(dateRange.from, "LLL dd, y")
                    )
                  ) : (
                    <span>Pick a date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={dateRange.from}
                  selected={dateRange}
                  onSelect={(range) => {
                    if (range?.from && range?.to) {
                      setDateRange({ from: range.from, to: range.to });
                    }
                  }}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-neutral-500">Average Rating</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold">4.6</span>
                <span className="text-green-500 ml-2 text-sm">+0.2</span>
                <span className="text-neutral-500 ml-1 text-xs">vs last month</span>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-neutral-500">Total Reviews</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold">247</span>
                <span className="text-green-500 ml-2 text-sm">+18</span>
                <span className="text-neutral-500 ml-1 text-xs">vs last month</span>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-neutral-500">Response Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold">92%</span>
                <span className="text-green-500 ml-2 text-sm">+5%</span>
                <span className="text-neutral-500 ml-1 text-xs">vs last month</span>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-neutral-500">Avg. Response Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold">6h</span>
                <span className="text-green-500 ml-2 text-sm">-2h</span>
                <span className="text-neutral-500 ml-1 text-xs">vs last month</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Analytics Content */}
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="ratings">Ratings</TabsTrigger>
            <TabsTrigger value="volume">Volume</TabsTrigger>
            <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Average Rating Trend</CardTitle>
                  <CardDescription>Rating performance over time</CardDescription>
                </CardHeader>
                <CardContent className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={monthlyRatingsData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis domain={[0, 5]} />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="rating"
                        stroke="#4285F4"
                        activeDot={{ r: 8 }}
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Reviews by Platform</CardTitle>
                  <CardDescription>Distribution across platforms</CardDescription>
                </CardHeader>
                <CardContent className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={platformDistributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {platformDistributionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={PLATFORM_COLORS[index % PLATFORM_COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Review Volume by Platform</CardTitle>
                  <CardDescription>Monthly review counts</CardDescription>
                </CardHeader>
                <CardContent className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={reviewVolumeData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="google" stackId="a" fill="#4285F4" />
                      <Bar dataKey="booking" stackId="a" fill="#003580" />
                      <Bar dataKey="airbnb" stackId="a" fill="#FF5A5F" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Sentiment Analysis</CardTitle>
                  <CardDescription>Positive, neutral, and negative reviews</CardDescription>
                </CardHeader>
                <CardContent className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={sentimentData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {sentimentData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={SENTIMENT_COLORS[index % SENTIMENT_COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="ratings">
            <div className="grid grid-cols-1 gap-6">
              <Card className="col-span-full">
                <CardHeader>
                  <CardTitle>Monthly Average Ratings</CardTitle>
                  <CardDescription>Trend of average ratings across all platforms</CardDescription>
                </CardHeader>
                <CardContent className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={monthlyRatingsData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis domain={[0, 5]} />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="rating"
                        stroke="#4285F4"
                        activeDot={{ r: 8 }}
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="volume">
            <div className="grid grid-cols-1 gap-6">
              <Card className="col-span-full">
                <CardHeader>
                  <CardTitle>Review Volume by Platform</CardTitle>
                  <CardDescription>Monthly review counts</CardDescription>
                </CardHeader>
                <CardContent className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={reviewVolumeData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="google" stackId="a" fill="#4285F4" />
                      <Bar dataKey="booking" stackId="a" fill="#003580" />
                      <Bar dataKey="airbnb" stackId="a" fill="#FF5A5F" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="sentiment">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Sentiment Distribution</CardTitle>
                  <CardDescription>Positive, neutral, and negative reviews</CardDescription>
                </CardHeader>
                <CardContent className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={sentimentData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {sentimentData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={SENTIMENT_COLORS[index % SENTIMENT_COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Top Mentioned Keywords</CardTitle>
                  <CardDescription>Most common terms in reviews</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h4 className="text-sm font-medium mb-2 text-green-600">Positive Keywords</h4>
                      <div className="space-y-2">
                        {topPositiveKeywords.map((item, i) => (
                          <div key={i} className="flex items-center">
                            <div className="w-32 font-medium">{item.keyword}</div>
                            <div className="flex-1">
                              <div className="h-2 bg-neutral-100 rounded-full overflow-hidden">
                                <div 
                                  className="h-full bg-green-500 rounded-full" 
                                  style={{ width: `${(item.count / topPositiveKeywords[0].count) * 100}%` }}
                                />
                              </div>
                            </div>
                            <div className="w-10 text-right text-sm text-neutral-500">{item.count}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium mb-2 text-red-600">Negative Keywords</h4>
                      <div className="space-y-2">
                        {topNegativeKeywords.map((item, i) => (
                          <div key={i} className="flex items-center">
                            <div className="w-32 font-medium">{item.keyword}</div>
                            <div className="flex-1">
                              <div className="h-2 bg-neutral-100 rounded-full overflow-hidden">
                                <div 
                                  className="h-full bg-red-500 rounded-full" 
                                  style={{ width: `${(item.count / topNegativeKeywords[0].count) * 100}%` }}
                                />
                              </div>
                            </div>
                            <div className="w-10 text-right text-sm text-neutral-500">{item.count}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
}