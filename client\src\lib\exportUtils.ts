import { jsPDF } from 'jspdf';
import { Review } from '@shared/schema';

export const formatDate = (dateString: string | Date): string => {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// Function to safely escape strings for CSV
const escapeCSV = (value: string | number | boolean | null | undefined): string => {
  if (value === null || value === undefined) return '';
  const stringValue = String(value);
  
  // If the string contains a comma, double quote, or newline, wrap in quotes and escape any quotes
  if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
    return `"${stringValue.replace(/"/g, '""')}"`;
  }
  
  return stringValue;
};

export const exportToCSV = (reviews: Review[]): string => {
  // Define headers
  const headers = [
    'ID', 'Platform', 'Author', 'Rating', 'Date', 'Content', 
    'Has Reply', 'Reply Content', 'Reply Date'
  ];
  
  // Map reviews to CSV rows
  const rows = reviews.map(review => [
    review.id,
    review.platform,
    review.authorName,
    review.rating,
    formatDate(review.date),
    review.content,
    review.isReplied ? 'Yes' : 'No',
    review.reply?.content || '',
    review.reply?.date ? formatDate(review.reply.date) : ''
  ]);
  
  // Combine headers and rows, and join with line breaks
  return [
    headers.map(escapeCSV).join(','),
    ...rows.map(row => row.map(escapeCSV).join(','))
  ].join('\n');
};

export const downloadFile = (data: string, filename: string, mimeType: string): void => {
  const blob = new Blob([data], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.click();
  URL.revokeObjectURL(url);
};

export const downloadCSV = (reviews: Review[]): void => {
  const csv = exportToCSV(reviews);
  const date = new Date().toISOString().slice(0, 10);
  downloadFile(csv, `reviews-export-${date}.csv`, 'text/csv;charset=utf-8;');
};

// Helper function to add multiline text with line wrapping
const addWrappedText = (doc: jsPDF, text: string, x: number, y: number, maxWidth: number, fontSize: number = 10): number => {
  const lineHeight = fontSize * 0.5;
  const lines = doc.splitTextToSize(text, maxWidth);
  doc.text(lines, x, y);
  return y + (lines.length * lineHeight);
};

export const generatePDF = (reviews: Review[]): Promise<Blob> => {
  return new Promise((resolve) => {
    try {
      // Create a new PDF document (A4 size)
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });
      
      const pageWidth = doc.internal.pageSize.getWidth();
      const margin = 20;
      const effectiveWidth = pageWidth - (margin * 2);
      
      // Add title
      doc.setFontSize(18);
      doc.text('Reviews Export', pageWidth / 2, margin, { align: 'center' });
      
      // Add generation date
      doc.setFontSize(10);
      doc.text(`Generated on: ${new Date().toLocaleString()}`, pageWidth / 2, margin + 8, { align: 'center' });
      
      let y = margin + 20;
      
      // For each review
      reviews.forEach((review, index) => {
        // Check if we need a new page
        if (index > 0 && y > 250) {
          doc.addPage();
          y = margin;
        }
        
        // Review header
        doc.setFontSize(14);
        doc.setTextColor(0, 0, 0);
        doc.text(`Review #${index + 1}: ${review.platform}`, margin, y);
        doc.setLineWidth(0.5);
        doc.line(margin, y + 1, margin + 50, y + 1);
        y += 6;
        
        // Review metadata
        doc.setFontSize(11);
        doc.setTextColor(68, 68, 68);
        doc.text(`Author: ${review.authorName}`, margin, y);
        y += 5;
        doc.text(`Rating: ${review.rating} / 5`, margin, y);
        y += 5;
        doc.text(`Date: ${formatDate(review.date)}`, margin, y);
        y += 7;
        
        // Review content
        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        doc.text('Review Content:', margin, y);
        y += 5;
        doc.setFontSize(10);
        doc.setTextColor(51, 51, 51);
        y = addWrappedText(doc, review.content, margin, y, effectiveWidth) + 5;
        
        // Reply if available
        if (review.isReplied && review.reply) {
          doc.setFontSize(12);
          doc.setTextColor(0, 0, 0);
          doc.text('Reply:', margin, y);
          y += 5;
          doc.setFontSize(10);
          doc.setTextColor(51, 51, 51);
          y = addWrappedText(doc, review.reply.content, margin, y, effectiveWidth) + 3;
          doc.text(`Replied on: ${formatDate(review.reply.date)}`, margin, y);
          y += 5;
        }
        
        // Divider
        if (index < reviews.length - 1) {
          doc.setDrawColor(170, 170, 170);
          doc.setLineWidth(0.3);
          doc.line(margin, y + 3, pageWidth - margin, y + 3);
          y += 10;
        }
      });
      
      // Get the PDF as a blob
      const pdfBlob = doc.output('blob');
      resolve(pdfBlob);
    } catch (error) {
      console.error('Error generating PDF:', error);
      // Create an empty PDF in case of error
      const doc = new jsPDF();
      doc.text('Error generating PDF', 10, 10);
      resolve(doc.output('blob'));
    }
  });
};

export const downloadPDF = async (reviews: Review[]): Promise<void> => {
  const blob = await generatePDF(reviews);
  const date = new Date().toISOString().slice(0, 10);
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `reviews-export-${date}.pdf`;
  link.click();
  URL.revokeObjectURL(url);
};