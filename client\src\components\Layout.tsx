import { ReactNode } from "react";
import Sidebar from "./Sidebar";

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar */}
      <Sidebar />

      {/* Main content */}
      <main className="flex-1 overflow-y-auto bg-neutral-50 pt-5 pb-10">
        <div className="md:px-8 px-4 mt-0 md:mt-0">
          {children}
        </div>
      </main>
    </div>
  );
}
