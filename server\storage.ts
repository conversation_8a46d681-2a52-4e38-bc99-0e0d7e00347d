import { 
  users, hotels, platformTokens, reviews, replies,
  User, InsertUser, 
  Review, InsertReview, 
  Reply, InsertReply,
  Hotel, InsertHotel,
  PlatformToken, InsertPlatformToken
} from "@shared/schema";

import session from "express-session";
import { eq, and, asc, desc, like, sql, count } from "drizzle-orm";
import { db } from "./db";
import connectPg from "connect-pg-simple";
import { Pool } from "@neondatabase/serverless";
import createMemoryStore from "memorystore";

const MemoryStore = createMemoryStore(session);
const pool = new Pool({ connectionString: process.env.DATABASE_URL });
const PostgresSessionStore = connectPg(session);

// Storage interface for the application
export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Hotel operations
  getHotel(id: number): Promise<Hotel | undefined>;
  createHotel(hotel: InsertHotel): Promise<Hotel>;
  listHotels(): Promise<Hotel[]>;
  
  // Platform token operations
  getPlatformToken(hotelId: number, platform: string): Promise<PlatformToken | undefined>;
  savePlatformToken(token: InsertPlatformToken): Promise<PlatformToken>;
  
  // Review operations
  getReview(id: number): Promise<Review | undefined>;
  getReviewByExternalId(platform: string, externalId: string): Promise<Review | undefined>;
  listReviews(hotelId: number, filters?: {
    platform?: string,
    isReplied?: boolean,
    search?: string,
    sortBy?: string,
    page?: number,
    limit?: number
  }): Promise<{ reviews: Review[], total: number }>;
  saveReview(review: InsertReview): Promise<Review>;
  
  // Reply operations
  getReply(id: number): Promise<Reply | undefined>;
  getReplyByReviewId(reviewId: number): Promise<Reply | undefined>;
  saveReply(reply: InsertReply): Promise<Reply>;
  markReviewAsReplied(reviewId: number): Promise<void>;
  
  // Session store for authentication
  sessionStore: any;
}

// In-memory storage implementation
export class DatabaseStorage implements IStorage {
  sessionStore: any;

  constructor() {
    this.sessionStore = new PostgresSessionStore({ 
      pool, 
      createTableIfMissing: true 
    });
    
    // Create a default hotel for demonstration
    this.checkAndCreateDefaultHotel();
  }

  private async checkAndCreateDefaultHotel() {
    const existingHotels = await db.select().from(hotels);
    if (existingHotels.length === 0) {
      await this.createHotel({
        name: "Hotel Majestic",
        address: "123 Main Street, Anytown, USA"
      });
    }
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const results = await db.select().from(users).where(eq(users.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const results = await db.select().from(users).where(eq(users.username, username));
    return results.length > 0 ? results[0] : undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const results = await db.insert(users)
      .values({ ...insertUser, role: "staff", hotelId: 1 })
      .returning();
    return results[0];
  }

  // Hotel operations
  async getHotel(id: number): Promise<Hotel | undefined> {
    const results = await db.select().from(hotels).where(eq(hotels.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async createHotel(insertHotel: InsertHotel): Promise<Hotel> {
    const results = await db.insert(hotels)
      .values(insertHotel)
      .returning();
    return results[0];
  }

  async listHotels(): Promise<Hotel[]> {
    return db.select().from(hotels);
  }

  // Platform token operations
  async getPlatformToken(hotelId: number, platform: string): Promise<PlatformToken | undefined> {
    const results = await db.select().from(platformTokens)
      .where(
        and(
          eq(platformTokens.hotelId, hotelId),
          eq(platformTokens.platform, platform)
        )
      );
    return results.length > 0 ? results[0] : undefined;
  }

  async savePlatformToken(insertToken: InsertPlatformToken): Promise<PlatformToken> {
    // Check if token already exists for this hotel/platform combination
    const existingToken = await this.getPlatformToken(insertToken.hotelId, insertToken.platform);
    
    if (existingToken) {
      // Update existing token
      const results = await db.update(platformTokens)
        .set({ accessToken: insertToken.accessToken, refreshToken: insertToken.refreshToken, expiresAt: insertToken.expiresAt })
        .where(eq(platformTokens.id, existingToken.id))
        .returning();
      return results[0];
    } else {
      // Create new token
      const results = await db.insert(platformTokens)
        .values(insertToken)
        .returning();
      return results[0];
    }
  }

  // Review operations
  async getReview(id: number): Promise<Review | undefined> {
    const results = await db.select().from(reviews).where(eq(reviews.id, id));
    if (results.length === 0) return undefined;
    
    const review = results[0];
    
    // Check if there's a reply for this review
    const reply = await this.getReplyByReviewId(id);
    if (reply) {
      return { ...review, reply };
    }
    return review;
  }

  async getReviewByExternalId(platform: string, externalId: string): Promise<Review | undefined> {
    const results = await db.select().from(reviews)
      .where(
        and(
          eq(reviews.platform, platform),
          eq(reviews.externalId, externalId)
        )
      );
    
    if (results.length === 0) return undefined;
    
    const review = results[0];
    
    // Check if there's a reply for this review
    const reply = await this.getReplyByReviewId(review.id);
    if (reply) {
      return { ...review, reply };
    }
    return review;
  }

  async listReviews(hotelId: number, filters?: {
    platform?: string,
    isReplied?: boolean,
    search?: string,
    sortBy?: string,
    page?: number,
    limit?: number
  }): Promise<{ reviews: Review[], total: number }> {
    // Build the where conditions
    let whereConditions = [eq(reviews.hotelId, hotelId)];
    
    if (filters) {
      if (filters.platform && filters.platform !== 'all') {
        whereConditions.push(eq(reviews.platform, filters.platform));
      }
      
      if (filters.isReplied !== undefined) {
        whereConditions.push(eq(reviews.isReplied, filters.isReplied));
      }
      
      if (filters.search) {
        const searchPattern = `%${filters.search}%`;
        whereConditions.push(
          sql`(${reviews.content} ILIKE ${searchPattern} OR ${reviews.authorName} ILIKE ${searchPattern})`
        );
      }
    }
    
    // Count total matching reviews
    const countResult = await db.select({ count: count() })
      .from(reviews)
      .where(and(...whereConditions));
    
    const total = Number(countResult[0].count);
    
    // Build the query for reviews
    let reviewsList: typeof reviews.$inferSelect[] = [];
    
    // Determine sort order based on filters
    const sortBy = filters?.sortBy || 'date-desc';
    
    // Apply correct Drizzle query based on sort and pagination
    try {
      // Use a simpler approach just using the Drizzle query builder with separate cases
      // to work around TypeScript limitations
      if (filters?.page !== undefined && filters?.limit !== undefined) {
        // With pagination
        const offset = (filters.page - 1) * filters.limit;
        
        if (sortBy === 'date-desc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.date))
            .limit(filters.limit)
            .offset(offset);
        } else if (sortBy === 'date-asc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(asc(reviews.date))
            .limit(filters.limit)
            .offset(offset);
        } else if (sortBy === 'rating-desc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.rating))
            .limit(filters.limit)
            .offset(offset);
        } else if (sortBy === 'rating-asc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(asc(reviews.rating))
            .limit(filters.limit)
            .offset(offset);
        } else {
          // Default sort
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.date))
            .limit(filters.limit)
            .offset(offset);
        }
      } else {
        // Without pagination
        if (sortBy === 'date-desc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.date));
        } else if (sortBy === 'date-asc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(asc(reviews.date));
        } else if (sortBy === 'rating-desc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.rating));
        } else if (sortBy === 'rating-asc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(asc(reviews.rating));
        } else {
          // Default sort
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.date));
        }
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      // Return empty list on error
      reviewsList = [];
    }
    
    // Attach replies to reviews
    const reviewsWithReplies = await Promise.all(
      reviewsList.map(async (review) => {
        const reply = await this.getReplyByReviewId(review.id);
        if (reply) {
          return { ...review, reply };
        }
        return review;
      })
    );
    
    return { reviews: reviewsWithReplies, total };
  }

  async saveReview(insertReview: InsertReview): Promise<Review> {
    const results = await db.insert(reviews)
      .values({ ...insertReview, isReplied: false })
      .returning();
    return results[0];
  }

  // Reply operations
  async getReply(id: number): Promise<Reply | undefined> {
    const results = await db.select().from(replies).where(eq(replies.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async getReplyByReviewId(reviewId: number): Promise<Reply | undefined> {
    const results = await db.select().from(replies).where(eq(replies.reviewId, reviewId));
    return results.length > 0 ? results[0] : undefined;
  }

  async saveReply(insertReply: InsertReply): Promise<Reply> {
    const results = await db.insert(replies)
      .values({ 
        ...insertReply, 
        date: new Date(), 
        isPosted: false 
      })
      .returning();
    
    // Mark the review as replied
    await this.markReviewAsReplied(insertReply.reviewId);
    
    return results[0];
  }

  async markReviewAsReplied(reviewId: number): Promise<void> {
    await db.update(reviews)
      .set({ isReplied: true })
      .where(eq(reviews.id, reviewId));
  }
}

export const storage = new DatabaseStorage();
