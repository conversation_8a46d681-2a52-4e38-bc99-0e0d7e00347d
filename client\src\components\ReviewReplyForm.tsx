import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FaReply, FaInfoCircle, FaEye } from "react-icons/fa";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "../lib/queryClient";

interface ReviewReplyFormProps {
  reviewId: number;
  onClose?: () => void;
}

export default function ReviewReplyForm({ reviewId, onClose }: ReviewReplyFormProps) {
  const [content, setContent] = useState("");
  const [showForm, setShowForm] = useState(false);
  const { toast } = useToast();

  const replyMutation = useMutation({
    mutationFn: async (replyData: { content: string }) => {
      const res = await apiRequest("POST", `/api/reviews/${reviewId}/reply`, replyData);
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Reply submitted",
        description: "Your response has been sent successfully.",
      });
      setContent("");
      setShowForm(false);
      if (onClose) onClose();
      
      // Invalidate the reviews query to refresh the list
      queryClient.invalidateQueries({ queryKey: ["/api/reviews"] });
    },
    onError: (error) => {
      toast({
        title: "Reply failed",
        description: error.message || "Failed to submit your reply. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = () => {
    if (!content.trim()) {
      toast({
        title: "Reply content required",
        description: "Please enter a reply before submitting.",
        variant: "destructive",
      });
      return;
    }

    replyMutation.mutate({ content });
  };

  if (!showForm) {
    return (
      <div className="mt-4">
        <button 
          onClick={() => setShowForm(true)}
          className="inline-flex items-center text-sm text-primary hover:text-primary-800 font-medium"
        >
          <FaReply className="mr-1" /> Write a reply
        </button>
      </div>
    );
  }

  return (
    <div className="mt-4">
      <div className="mt-2 rounded-md border border-neutral-300 overflow-hidden">
        <textarea 
          rows={3} 
          value={content}
          onChange={(e) => setContent(e.target.value)}
          className="block w-full border-0 py-2 px-3 text-neutral-700 placeholder-neutral-400 focus:ring-primary focus:border-primary sm:text-sm" 
          placeholder="Type your reply here..."
        />
        <div className="bg-neutral-50 px-3 py-2 flex justify-between items-center">
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="text-xs h-8 bg-primary-100 text-primary-700 hover:bg-primary-200 border-none"
            >
              <FaInfoCircle className="mr-1" /> Guidelines
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="text-xs h-8 bg-neutral-100 text-neutral-700 hover:bg-neutral-200 border-none"
            >
              <FaEye className="mr-1" /> Preview
            </Button>
          </div>
          <div>
            <Button 
              size="sm" 
              className="h-8 text-xs" 
              onClick={handleSubmit}
              disabled={replyMutation.isPending}
            >
              {replyMutation.isPending ? "Sending..." : "Send Reply"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
